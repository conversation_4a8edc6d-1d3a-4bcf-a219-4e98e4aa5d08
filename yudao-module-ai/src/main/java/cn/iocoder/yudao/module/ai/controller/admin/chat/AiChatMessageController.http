### 发送消息（段式）
POST {{baseUrl}}/ai/chat/message/send
Content-Type: application/json
Authorization: {{token}}
tenant-id: {{adminTenantId}}

{
  "conversationId": "1781604279872581724",
  "content": "你是 OpenAI 么？"
}

### 发送消息（流式）
POST {{baseUrl}}/ai/chat/message/send-stream
Content-Type: application/json
Authorization: {{token}}
tenant-id: {{adminTenantId}}

{
  "conversationId": "1781604279872581724",
  "content": "1+1=?"
}

### 获得指定对话的消息列表
GET {{baseUrl}}/ai/chat/message/list-by-conversation-id?conversationId=1781604279872581649
Authorization: {{token}}

### 删除消息
DELETE {{baseUrl}}/ai/chat/message/delete?id=50
Authorization: {{token}}