package cn.iocoder.yudao.module.infra.service.demo;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.infra.controller.admin.demo.vo.*;
import cn.iocoder.yudao.module.infra.dal.dataobject.demo.InfraStudentDO;
import cn.iocoder.yudao.module.infra.dal.dataobject.demo.InfraStudentContactDO;
import cn.iocoder.yudao.module.infra.dal.dataobject.demo.InfraStudentTeacherDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 学生 Service 接口
 *
 * <AUTHOR>
 */
public interface InfraStudentService {

    /**
     * 创建学生
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createStudent(@Valid InfraStudentSaveReqVO createReqVO);

    /**
     * 更新学生
     *
     * @param updateReqVO 更新信息
     */
    void updateStudent(@Valid InfraStudentSaveReqVO updateReqVO);

    /**
     * 删除学生
     *
     * @param id 编号
     */
    void deleteStudent(Long id);

    /**
     * 获得学生
     *
     * @param id 编号
     * @return 学生
     */
    InfraStudentDO getStudent(Long id);

    /**
     * 获得学生分页
     *
     * @param pageReqVO 分页查询
     * @return 学生分页
     */
    PageResult<InfraStudentDO> getStudentPage(InfraStudentPageReqVO pageReqVO);

    // ==================== 子表（学生联系人） ====================

    /**
     * 获得学生联系人分页
     *
     * @param pageReqVO 分页查询
     * @param studentId 学生编号
     * @return 学生联系人分页
     */
    PageResult<InfraStudentContactDO> getStudentContactPage(PageParam pageReqVO, Long studentId);

    /**
     * 创建学生联系人
     *
     * @param studentContact 创建信息
     * @return 编号
     */
    Long createStudentContact(@Valid InfraStudentContactDO studentContact);

    /**
     * 更新学生联系人
     *
     * @param studentContact 更新信息
     */
    void updateStudentContact(@Valid InfraStudentContactDO studentContact);

    /**
     * 删除学生联系人
     *
     * @param id 编号
     */
    void deleteStudentContact(Long id);

	/**
	 * 获得学生联系人
	 *
	 * @param id 编号
     * @return 学生联系人
	 */
    InfraStudentContactDO getStudentContact(Long id);

    // ==================== 子表（学生班主任） ====================

    /**
     * 获得学生班主任分页
     *
     * @param pageReqVO 分页查询
     * @param studentId 学生编号
     * @return 学生班主任分页
     */
    PageResult<InfraStudentTeacherDO> getStudentTeacherPage(PageParam pageReqVO, Long studentId);

    /**
     * 创建学生班主任
     *
     * @param studentTeacher 创建信息
     * @return 编号
     */
    Long createStudentTeacher(@Valid InfraStudentTeacherDO studentTeacher);

    /**
     * 更新学生班主任
     *
     * @param studentTeacher 更新信息
     */
    void updateStudentTeacher(@Valid InfraStudentTeacherDO studentTeacher);

    /**
     * 删除学生班主任
     *
     * @param id 编号
     */
    void deleteStudentTeacher(Long id);

	/**
	 * 获得学生班主任
	 *
	 * @param id 编号
     * @return 学生班主任
	 */
    InfraStudentTeacherDO getStudentTeacher(Long id);

}