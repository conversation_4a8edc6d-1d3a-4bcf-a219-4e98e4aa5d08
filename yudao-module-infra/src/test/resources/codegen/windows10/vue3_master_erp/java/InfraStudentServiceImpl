package cn.iocoder.yudao.module.infra.service.demo;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.infra.controller.admin.demo.vo.*;
import cn.iocoder.yudao.module.infra.dal.dataobject.demo.InfraStudentDO;
import cn.iocoder.yudao.module.infra.dal.dataobject.demo.InfraStudentContactDO;
import cn.iocoder.yudao.module.infra.dal.dataobject.demo.InfraStudentTeacherDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.infra.dal.mysql.demo.InfraStudentMapper;
import cn.iocoder.yudao.module.infra.dal.mysql.demo.InfraStudentContactMapper;
import cn.iocoder.yudao.module.infra.dal.mysql.demo.InfraStudentTeacherMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.infra.enums.ErrorCodeConstants.*;

/**
 * 学生 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InfraStudentServiceImpl implements InfraStudentService {

    @Resource
    private InfraStudentMapper studentMapper;
    @Resource
    private InfraStudentContactMapper studentContactMapper;
    @Resource
    private InfraStudentTeacherMapper studentTeacherMapper;

    @Override
    public Long createStudent(InfraStudentSaveReqVO createReqVO) {
        // 插入
        InfraStudentDO student = BeanUtils.toBean(createReqVO, InfraStudentDO.class);
        studentMapper.insert(student);
        // 返回
        return student.getId();
    }

    @Override
    public void updateStudent(InfraStudentSaveReqVO updateReqVO) {
        // 校验存在
        validateStudentExists(updateReqVO.getId());
        // 更新
        InfraStudentDO updateObj = BeanUtils.toBean(updateReqVO, InfraStudentDO.class);
        studentMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteStudent(Long id) {
        // 校验存在
        validateStudentExists(id);
        // 删除
        studentMapper.deleteById(id);

        // 删除子表
        deleteStudentContactByStudentId(id);
        deleteStudentTeacherByStudentId(id);
    }

    private void validateStudentExists(Long id) {
        if (studentMapper.selectById(id) == null) {
            throw exception(STUDENT_NOT_EXISTS);
        }
    }

    @Override
    public InfraStudentDO getStudent(Long id) {
        return studentMapper.selectById(id);
    }

    @Override
    public PageResult<InfraStudentDO> getStudentPage(InfraStudentPageReqVO pageReqVO) {
        return studentMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（学生联系人） ====================

    @Override
    public PageResult<InfraStudentContactDO> getStudentContactPage(PageParam pageReqVO, Long studentId) {
        return studentContactMapper.selectPage(pageReqVO, studentId);
    }

    @Override
    public Long createStudentContact(InfraStudentContactDO studentContact) {
        studentContactMapper.insert(studentContact);
        return studentContact.getId();
    }

    @Override
    public void updateStudentContact(InfraStudentContactDO studentContact) {
        // 校验存在
        validateStudentContactExists(studentContact.getId());
        // 更新
        studentContactMapper.updateById(studentContact);
    }

    @Override
    public void deleteStudentContact(Long id) {
        // 校验存在
        validateStudentContactExists(id);
        // 删除
        studentContactMapper.deleteById(id);
    }

    @Override
    public InfraStudentContactDO getStudentContact(Long id) {
        return studentContactMapper.selectById(id);
    }

    private void validateStudentContactExists(Long id) {
        if (studentContactMapper.selectById(id) == null) {
            throw exception(STUDENT_CONTACT_NOT_EXISTS);
        }
    }

    private void deleteStudentContactByStudentId(Long studentId) {
        studentContactMapper.deleteByStudentId(studentId);
    }

    // ==================== 子表（学生班主任） ====================

    @Override
    public PageResult<InfraStudentTeacherDO> getStudentTeacherPage(PageParam pageReqVO, Long studentId) {
        return studentTeacherMapper.selectPage(pageReqVO, studentId);
    }

    @Override
    public Long createStudentTeacher(InfraStudentTeacherDO studentTeacher) {
        // 校验是否已经存在
        if (studentTeacherMapper.selectByStudentId(studentTeacher.getStudentId()) != null) {
            throw exception(STUDENT_TEACHER_EXISTS);
        }
        // 插入
        studentTeacherMapper.insert(studentTeacher);
        return studentTeacher.getId();
    }

    @Override
    public void updateStudentTeacher(InfraStudentTeacherDO studentTeacher) {
        // 校验存在
        validateStudentTeacherExists(studentTeacher.getId());
        // 更新
        studentTeacherMapper.updateById(studentTeacher);
    }

    @Override
    public void deleteStudentTeacher(Long id) {
        // 校验存在
        validateStudentTeacherExists(id);
        // 删除
        studentTeacherMapper.deleteById(id);
    }

    @Override
    public InfraStudentTeacherDO getStudentTeacher(Long id) {
        return studentTeacherMapper.selectById(id);
    }

    private void validateStudentTeacherExists(Long id) {
        if (studentTeacherMapper.selectById(id) == null) {
            throw exception(STUDENT_TEACHER_NOT_EXISTS);
        }
    }

    private void deleteStudentTeacherByStudentId(Long studentId) {
        studentTeacherMapper.deleteByStudentId(studentId);
    }

}