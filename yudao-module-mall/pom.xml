<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>yudao</artifactId>
        <groupId>cn.iocoder.boot</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>yudao-module-mall</artifactId>
    <packaging>pom</packaging>

    <name>${project.artifactId}</name>

    <description>
        商城大模块，由 product 商品、promotion 营销、trade 交易、statistics 统计等组成
    </description>
    <modules>
        <module>yudao-module-product</module>
        <module>yudao-module-promotion</module>
        <module>yudao-module-trade</module>
        <module>yudao-module-statistics</module>
        <!--
            特殊：为什么会有 yudao-module-trade-api 呢？
                yudao-module-promotion 和 yudao-module-trade 之间相互循环依赖，所以抽出 yudao-module-trade-api 模块，这样：
                1. yudao-module-promotion 依赖 yudao-module-trade-api
                2. yudao-module-trade 依赖 yudao-module-promotion
            从而不存在相互（循环）依赖，即 yudao-module-trade => yudao-module-promotion => yudao-module-trade-api
         -->
        <module>yudao-module-trade-api</module>
    </modules>

</project>
